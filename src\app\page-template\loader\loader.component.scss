// .pageOverlayLoader {
//     height: 100%;
//     width: 100%;
//     background: rgba(256,256,256,0.4);
//     margin: 0 auto;
//     position: fixed;
//     z-index: 100;
//     left: 0;
//     top:0;
//   }
//   .loaderholder {
//       margin: 0 auto;
//       position: fixed;
//       width: 60px;
//       height: 60px;
//       left: 45%;
//       top: 45%;
//       text-align: center;
//       color: #f28b2f;
//     }
  
//     .tidc-swaagat-cube {
//       width: 50px;
//       height: 50px;
  
//       -webkit-transform: rotateZ(45deg);
//       transform: rotateZ(45deg);
//     }
  
//     .tidc-swaagat-cube .tidc-innerdiv {
//       float: left;
//       width: 50%;
//       height: 50%;
//       position: relative;
//       -webkit-transform: scale(1.1);
//       -ms-transform: scale(1.1);
//       transform: scale(1.1);
//     }
//     .tidc-swaagat-cube .tidc-innerdiv:before {
//       content: "";
//       position: absolute;
//       top: 0;
//       left: 0;
//       width: 100%;
//       height: 100%;
//       background-color: #f28b2f;
//       -webkit-animation: tidc-fold-Angle 2.4s infinite linear both;
//       animation: tidc-fold-Angle 2.4s infinite linear both;
//       -webkit-transform-origin: 100% 100%;
//       -ms-transform-origin: 100% 100%;
//       transform-origin: 100% 100%;
//     }
//     .tidc-swaagat-cube .tidc-innerdiv2 {
//       -webkit-transform: scale(1.1) rotateZ(90deg);
//       transform: scale(1.1) rotateZ(90deg);
//     }
//     .tidc-swaagat-cube .tidc-innerdiv3 {
//       -webkit-transform: scale(1.1) rotateZ(180deg);
//       transform: scale(1.1) rotateZ(180deg);
//     }
//     .tidc-swaagat-cube .tidc-innerdiv4 {
//       -webkit-transform: scale(1.1) rotateZ(270deg);
//       transform: scale(1.1) rotateZ(270deg);
//     }
//     .tidc-swaagat-cube .tidc-innerdiv2:before {
//       -webkit-animation-delay: 0.3s;
//       animation-delay: 0.3s;
//     }
//     .tidc-swaagat-cube .tidc-innerdiv3:before {
//       -webkit-animation-delay: 0.6s;
//       animation-delay: 0.6s;
//     }
//     .tidc-swaagat-cube .tidc-innerdiv4:before {
//       -webkit-animation-delay: 0.9s;
//       animation-delay: 0.9s;
//     }
//     @-webkit-keyframes tidc-fold-Angle {
//       0%,
//       10% {
//         -webkit-transform: perspective(140px) rotateX(-180deg);
//         transform: perspective(140px) rotateX(-180deg);
//         opacity: 0;
//       }
//       25%,
//       75% {
//         -webkit-transform: perspective(140px) rotateX(0deg);
//         transform: perspective(140px) rotateX(0deg);
//         opacity: 1;
//       }
//       90%,
//       100% {
//         -webkit-transform: perspective(140px) rotateY(180deg);
//         transform: perspective(140px) rotateY(180deg);
//         opacity: 0;
//       }
//     }
  
//     @keyframes tidc-fold-Angle {
//       0%,
//       10% {
//         -webkit-transform: perspective(140px) rotateX(-180deg);
//         transform: perspective(140px) rotateX(-180deg);
//         opacity: 0;
//       }
//       25%,
//       75% {
//         -webkit-transform: perspective(140px) rotateX(0deg);
//         transform: perspective(140px) rotateX(0deg);
//         opacity: 1;
//       }
//       90%,
//       100% {
//         -webkit-transform: perspective(140px) rotateY(180deg);
//         transform: perspective(140px) rotateY(180deg);
//         opacity: 0;
//       }
//     }



.loader::before,
.loader::after {
  content: "";
  border-radius: 5px;
  position: absolute;
  inset: 0;
  background: #514b82;
  clip-path: polygon(0 0, 100% 0, 100% 67%, 50% 67%, 50% 34%, 0 34%);
  animation: sp7 2s infinite;
}


.loader {
  --loader-width: 68px;
  --loader-height: 100px;
  width: var(--loader-width);
  height: var(--loader-height);
  position: relative;
  border-radius: 10px;
}
.loader::after {
  --s: -1, -1;
}

@keyframes sp7 {
  0%, 10% {
    transform: scale(var(--s, 1)) translate(0, 0) rotate(0deg);
  }
  33% {
    /* translate up by ~33% of height */
    transform: scale(var(--s, 1)) translate(0, calc(-0.33 * var(--loader-height))) rotate(0deg);
  }
  66% {
    /* translate right by 25% width and up by 33% height */
    transform: scale(var(--s, 1)) translate(calc(0.25 * var(--loader-width)), calc(-0.33 * var(--loader-height))) rotate(-90deg);
  }
  90%, 100% {
    /* translate right 25% width and up by 16.6% height */
    transform: scale(var(--s, 1)) translate(calc(0.25 * var(--loader-width)), calc(-0.166 * var(--loader-height))) rotate(-90deg);
  }
}

.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.7); /* optional: dim the background */
  z-index: 11111;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: all;  /* ensure it captures events */
}