.form-group {
  position: relative;
  margin-bottom: 1.25rem;
}

label {
  margin-bottom: 0.5rem; /* Controls space between label and input */
}
.bold-label {
  font-weight: 500 !important; /* Bold when readonly */
}



.red-font {
  color: var(--color-error, #dc3545);
  font-weight: 700;
  margin-left: 0.25rem;
}

.invalid-input {
  opacity: 0;
  color: var(--color-error, #dc3545);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  transform: translateY(-4px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.invalid-input.show-error {
  opacity: 1;
  transform: translateY(0);
}

.error-msg-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

.show-data {
  color: var(--color-text-primary, #1f2937);
  background-color: var(--color-background, #ffffff);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-border, #ced4da);
}

.w-100 {
  width: 100%;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  border: 1px solid var(--color-border, #ced4da);
  border-radius: 6px;
  background-color: var(--color-background, #ffffff);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline:hover,
:host ::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline {
  border-color: var(--color-brand-primary, #2563eb);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

:host ::ng-deep .mat-form-field-appearance-outline.is-invalid .mat-form-field-outline {
  border-color: var(--color-error, #dc3545);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

:host ::ng-deep .mat-form-field-flex {
  background-color: var(--color-background, #ffffff);
  border-radius: 6px;
}

:host ::ng-deep .mat-form-field-infix {
  padding: 0.5rem 0;
}

:host ::ng-deep .mat-select-value,
:host ::ng-deep .mat-select-arrow {
  color: var(--color-text-primary, #1f2937);
}

:host ::ng-deep .mat-select-panel {
  background-color: var(--color-background, #ffffff);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
}

:host ::ng-deep .mat-option {
  color: var(--color-text-primary, #1f2937);
  font-size: 0.875rem;
}

:host ::ng-deep .mat-option.mat-selected:not(.mat-option-disabled) {
  background-color: var(--color-brand-primary, #2563eb);
  color: #ffffff;
}

:host ::ng-deep .mat-option:hover:not(.mat-option-disabled),
:host ::ng-deep .mat-option.mat-active {
  background-color: rgba(37, 99, 235, 0.1);
}
