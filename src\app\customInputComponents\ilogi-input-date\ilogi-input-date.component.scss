.form-group {
  margin-bottom: 1rem;
}
label {
  margin-bottom: 0.5rem; /* Controls space between label and input */
}
.bold-label {
  font-weight: 500 !important; /* Bold when readonly */
}

.form-control.date-input {
  border: 1px solid var(--color-border, #ced4da);
  border-radius: 6px;
  background-color: var(--color-background, #ffffff);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  color: var(--color-text-primary, #333333);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control.date-input:focus {
  outline: none;
  border-color: var(--color-info, #0a54c4); /* Highlight on focus */
  box-shadow: 0 0 0 3px rgba(6, 41, 236, 0.2); /* Subtle glow effect */
}

.is-invalid.date-input-invalid {
  border-color: var(--color-error, #dc3545);
}
.is-invalid.date-input-invalid:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}


.invalid-input {
  opacity: 0;
  color: var(--color-error, #dc3545);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  transform: translateY(-4px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.invalid-input.show-error {
  opacity: 1;
  transform: translateY(0);
}

.red-font {
  color: #dc3545;
}

.show-data {
  margin-bottom: 0.5rem;
}

.x-error-msg-text {
  font-size: 0.875rem;
}

.date-input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.calendar-icon {
  position: absolute;
  right: 0.75rem; /* Adjusted for better alignment inside input */
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #333; /* Match input text color */
  font-size: 1.2rem;
}

/* Override global datepicker styles */
:host ::ng-deep .mat-datepicker-content {
  background-color: #ffffff !important;
  color: #ae0f0f !important;
  border-radius: 8px !important;
}

:host ::ng-deep .mat-calendar {
  background-color: #f86464 !important;
}

:host ::ng-deep .mat-calendar-body-cell-content {
  color: #333 !important;
}

:host ::ng-deep .mat-calendar-body-today {
  border-color: #333 !important;
}

:host ::ng-deep .mat-calendar-body-selected {
  background-color: var(--color-brand-primary, #a11220) !important;
  color: #ffffff !important;
}

:host ::ng-deep .mat-calendar-body-cell:hover .mat-calendar-body-cell-content {
  background-color: var(--color-red-100, #f9e1e6) !important;
}

.mat-calendar{
  background: var(--color-brand-primary) !important;
}

:host ::ng-deep .mat-calendar {
  background-color: #450303 !important;
}

:host ::ng-deep #mat-datepicker-2{
  background-color: #450303 !important;
  color: #ffffff !important;
}





