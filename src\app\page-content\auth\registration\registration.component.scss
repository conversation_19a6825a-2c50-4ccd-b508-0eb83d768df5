// Import your existing color variables
:root {
  // Your existing color variables from paste.txt
  --color-primary-bg: hsl(0, 0%, 100%);
  --color-card-bg: hsl(0, 0%, 100%);
  --color-surface: hsl(0, 0%, 98%);
  --color-brand-primary: hsl(348, 99%, 33%);
  --color-brand-secondary: hsl(348, 85%, 45%);
  --color-brand-accent: hsl(348, 100%, 40%);
  --color-brand-light: hsl(348, 100%, 95%);
  --color-brand-dark: hsl(348, 99%, 25%);
  --color-text-primary: hsl(0, 0%, 13%);
  --color-text-secondary: hsl(0, 0%, 40%);
  --color-text-muted: hsl(0, 0%, 60%);
  --color-border-light: hsl(0, 0%, 90%);
  --color-border-focus: hsl(348, 99%, 33%);
  --color-form-input-bg: hsl(0, 0%, 100%);
  --color-form-input-border: hsl(0, 0%, 85%);
  --color-form-input-border-focus: hsl(348, 99%, 33%);
  --color-error: hsl(348, 99%, 33%);
  --color-success: hsl(142, 76%, 45%);
  --shadow-light: hsla(0, 0%, 0%, 0.05);
  --shadow-medium: hsla(0, 0%, 0%, 0.1);
}

.registration-container {
  min-height: 100vh;
  background-color: var(--color-surface);
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: flex-start;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.registration-card {
  max-width: 800px;
  width: 100%;
  background-color: var(--color-card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 20px var(--shadow-medium);
  margin-top: 2rem;

  .mat-mdc-card-header {
    background-color: var(--color-brand-primary);
    color: white;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    margin: -1px -1px 0 -1px;
  }

  .registration-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;

    mat-icon {
      font-size: 1.8rem;
      width: 1.8rem;
      height: 1.8rem;
    }
  }

  .mat-mdc-card-content {
    padding: 2rem;
  }
}

.registration-form {
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .form-field {
    &.half-width {
      // Will automatically fit in grid
    }

    &.full-width {
      grid-column: 1 / -1;
    }

    .mat-mdc-form-field-label {
      color: var(--color-text-primary);
      font-weight: 500;
    }

    .mat-mdc-text-field-wrapper {
      background-color: var(--color-form-input-bg);
      border: 1px solid var(--color-form-input-border);
      border-radius: 8px;
      transition: border-color 0.2s ease;

      &:hover {
        border-color: var(--color-text-secondary);
      }

      &.mdc-text-field--focused {
        border-color: var(--color-form-input-border-focus);
      }
    }

    .mat-mdc-input-element {
      color: var(--color-text-primary);
      
      &::placeholder {
        color: var(--color-text-muted);
      }
    }

    .mat-mdc-form-field-error {
      color: var(--color-error);
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
  }

  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--color-border-light);
    border-radius: 8px;
    background-color: var(--color-form-input-bg);

    .field-label {
      color: var(--color-text-primary);
      font-weight: 500;
      font-size: 0.875rem;
      margin-bottom: 0.5rem;
    }

    .radio-buttons {
      display: flex;
      gap: 1.5rem;
      flex-wrap: wrap;

      .radio-option {
        .mat-mdc-radio-button {
          color: var(--color-text-primary);
        }

        .mat-mdc-radio-outer-circle {
          border-color: var(--color-border-focus);
        }

        .mat-mdc-radio-inner-circle {
          background-color: var(--color-brand-primary);
        }

        &.mat-mdc-radio-checked {
          .mat-mdc-radio-outer-circle {
            border-color: var(--color-brand-primary);
          }
        }
      }
    }

    .radio-error {
      color: var(--color-error);
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
  }

  .required-asterisk {
    color: var(--color-error);
    margin-left: 0.25rem;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border-light);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.75rem;
  }

  .reset-button {
    background-color: transparent;
    color: var(--color-text-secondary);
    border: 1px solid var(--color-border-light);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--color-surface);
      border-color: var(--color-text-secondary);
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }

  .submit-button {
    background-color: var(--color-brand-primary);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background-color: var(--color-brand-dark);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px var(--shadow-medium);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    mat-icon, mat-spinner {
      margin-right: 0.5rem;
    }
  }
}

// Custom Material Design overrides
.mat-mdc-form-field {
  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 0.25rem;
  }

  .mat-mdc-form-field-bottom-align::before {
    display: none;
  }
}

.mat-mdc-radio-group {
  .mat-mdc-radio-button {
    margin-bottom: 0.5rem;
  }
}

// Snackbar styles
.success-snackbar {
  background-color: var(--color-success) !important;
  color: white !important;
}

.error-snackbar {
  background-color: var(--color-error) !important;
  color: white !important;
}

// Loading spinner
.mat-mdc-progress-spinner {
  circle {
    stroke: white;
  }
}

// Responsive design
@media (max-width: 768px) {
  .registration-container {
    padding: 1rem;
  }

  .registration-card {
    margin-top: 1rem;
    
    .mat-mdc-card-content {
      padding: 1.5rem;
    }
  }

  .registration-form {
    .form-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .radio-group {
      .radio-buttons {
        flex-direction: column;
        gap: 0.75rem;
      }
    }
  }
}

// Focus styles for accessibility
.form-field:focus-within {
  .mat-mdc-text-field-wrapper {
    outline: 2px solid var(--color-brand-primary);
    outline-offset: 2px;
  }
}

.radio-group:focus-within {
  outline: 2px solid var(--color-brand-primary);
  outline-offset: 2px;
}