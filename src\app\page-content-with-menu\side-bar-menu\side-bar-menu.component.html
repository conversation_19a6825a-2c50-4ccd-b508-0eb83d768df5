<aside class="sidebar" [class.collapsed]="isCollapsed" [class.visible-mobile]="isVisible">
  <!-- Sidebar Header -->
  <div class="sidebar-header">
    <div class="brand-section" *ngIf="!isCollapsed">
      <div class="brand-logo">
        <span class="brand-name">Swaagat</span>
      </div>
    </div>
    <button class="toggle-btn" (click)="onToggle.emit()" [attr.aria-label]="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'">
      <span class="material-icons">{{ isCollapsed ? 'menu' : 'menu_open' }}</span>
    </button>
  </div>

  <!-- User Profile -->
  <div class="user-profile" *ngIf="!isCollapsed">
    <div class="user-info">
      <div class="user-name">Joydeep <PERSON>bnath</div>
      <div class="user-role">{{ userRole.toUpperCase() }}</div>
    </div>
    <div class="user-status">
      <span class="status-indicator online"></span>
    </div>
  </div>

  <!-- Sidebar Navigation -->
  <nav class="sidebar-nav">
    <ul class="nav-list">
      <ng-container *ngFor="let item of menuItems">
        <!-- Only show item if user has access -->
        <ng-container *ngIf="canAccess(item)">
          <!-- Parent item without children -->
          <li class="nav-item" *ngIf="!item.children">
            <a class="nav-link" (click)="navigate(item.route)" [attr.title]="isCollapsed ? item.title : null">
              <span class="nav-icon material-icons">{{ item.icon }}</span>
              <span class="nav-text" *ngIf="!isCollapsed">{{ item.title }}</span>
            </a>
          </li>

          <!-- Parent item with children -->
          <li class="nav-item" *ngIf="item.children">
            <div class="nav-link has-children" (click)="toggleSubmenu(item.id)" [class.expanded]="expandedSubmenu === item.id" [attr.title]="isCollapsed ? item.title : null">
              <span class="nav-icon material-icons">{{ item.icon }}</span>
              <span class="nav-text" *ngIf="!isCollapsed">{{ item.title }}</span>
              <span class="expand-icon material-icons" *ngIf="!isCollapsed">
                {{ expandedSubmenu === item.id ? 'expand_less' : 'expand_more' }}
              </span>
            </div>
            <ul class="submenu" *ngIf="expandedSubmenu === item.id && !isCollapsed">
              <li class="submenu-item" *ngFor="let child of item.children" [ngIf]="canAccess(child)">
                <a class="submenu-link" (click)="navigate(child.route)">
                  <span class="submenu-icon material-icons">{{ child.icon }}</span>
                  <span class="submenu-text">{{ child.title }}</span>
                </a>
              </li>
            </ul>
          </li>
        </ng-container>
      </ng-container>
    </ul>
  </nav>

  <!-- Footer -->
  <div class="sidebar-footer" *ngIf="!isCollapsed">
    <div class="footer-actions">
      <button class="footer-btn" title="Help & Support">
        <span class="material-icons">help</span>
        <span>Help</span>
      </button>
      <button class="footer-btn" title="Logout">
        <span class="material-icons">logout</span>
        <span>Logout</span>
      </button>
    </div>
  </div>

  <div class="sidebar-footer collapsed" *ngIf="isCollapsed">
    <button class="footer-btn-collapsed" title="Help & Support">
      <span class="material-icons">help</span>
    </button>
    <button class="footer-btn-collapsed" title="Logout">
      <span class="material-icons">logout</span>
    </button>
  </div>
</aside>
