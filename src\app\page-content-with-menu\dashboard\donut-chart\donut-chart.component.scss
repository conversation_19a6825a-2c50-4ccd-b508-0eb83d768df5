.donut-chart-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.chart-header {
  margin-bottom: 24px;
}
.chart-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}
.donut-chart-content {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .donut-chart-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .chart-header {
    margin-bottom: 24px;
  }
  .chart-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
  }
  .donut-chart-content {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;

    flex-direction: row;
  }
  .donut-chart-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .donut-chart {
    max-width: 200px;
    max-height: 200px;
  }
  .donut-segment {
    cursor: pointer;
    transition: opacity 0.2s ease;
  }
  .donut-segment:hover {
    opacity: 0.8;
  }
  .donut-center {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  .donut-total {
    font-size: 32px;
    font-weight: 700;
    color: #212529;
    line-height: 1;
  }
  .donut-total-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 4px;
  }
  .donut-legend {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-left: 40px;
    min-width: 200px;
  }
  .legend-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    cursor: pointer;
  }
  .legend-item:hover,
  .legend-item--active {
    background-color: #f8f9fa;
  }
  .legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    flex-shrink: 0;
  }
  .legend-content {
    flex: 1;
  }
  .legend-label {
    font-size: 14px;
    font-weight: 500;
    color: #212529;
    margin-bottom: 2px;
  }
  .legend-value {
    font-size: 12px;
    color: #6c757d;
  }

  @media (max-width: 768px) {
    .donut-chart-content {
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .donut-legend {
      margin-left: 0;
      margin-top: 24px;
      width: 100%;
      min-width: unset;
    }
  }
}
