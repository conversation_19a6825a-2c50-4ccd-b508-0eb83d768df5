@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');

/* =====================
   Root Variables
====================== */
:root {
  /* Main Theme Colors - 5 Colors Only */
  --color-primary: #fefae0;        /* 60% main color */
  --color-secondary: #ccd5ae;      /* 30% secondary color */
  --color-secondary-shade: #e9edc9; /* highlight color for the 30% */
  --color-tertiary: #d4a373;       /* 10% focus color */
  --color-tertiary-shade: #faedcd; /* shade and focus color of the 10% */

  /* Text Colors - 3 Black Shades */
  --color-text-dark: #1a1a1a;      /* Darkest text */
  --color-text-medium: #4a4a4a;    /* Medium text */
  --color-text-light: #7a7a7a;     /* Light text */

  /* Supporting Poppy Colors - 5 Colors */
  --color-poppy-red: #ff6b6b;      /* Bright red */
  --color-poppy-orange: #ff8e53;   /* Bright orange */
  --color-poppy-yellow: #ffd93d;   /* Bright yellow */
  --color-poppy-green: #6bcf7f;    /* Bright green */
  --color-poppy-blue: #4ecdc4;     /* Bright blue */
}

/* =====================
   Global Styles
====================== */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  background-color: var(--color-primary);
  color: var(--color-text-dark);
  margin: 0;
  line-height: 1.6;
}

.main-container {
  padding: 2rem;
  background-color: var(--color-primary);
  color: var(--color-text-dark);
  min-height: 100vh;
}

.page-layout {
  background-color: var(--color-tertiary-shade);
  min-height: 100vh;
}

/* =====================
   Form Elements Global Styling
====================== */
button:not(.mat-mdc-button):not(.mat-mdc-raised-button):not(.mat-mdc-outlined-button):not(.btn) {
  background-color: var(--color-tertiary);
  color: white;
  border: 1px solid var(--color-tertiary);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background-color: var(--color-secondary);
    border-color: var(--color-secondary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

input:not(.mat-mdc-input-element),
textarea:not(.mat-mdc-input-element),
select:not(.mat-mdc-select) {
  background-color: var(--color-secondary-shade);
  border: 1px solid var(--color-secondary);
  color: var(--color-text-dark);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    border-color: var(--color-tertiary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(212, 163, 115, 0.1);
  }

  &::placeholder {
    color: var(--color-text-light);
  }
}

/* =====================
   Utility Classes
====================== */
.text-dark { color: var(--color-text-dark); }
.text-medium { color: var(--color-text-medium); }
.text-light { color: var(--color-text-light); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-secondary-shade { background-color: var(--color-secondary-shade); }
.bg-tertiary { background-color: var(--color-tertiary); }
.bg-tertiary-shade { background-color: var(--color-tertiary-shade); }
