{"name": "swaagat-2", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^20.0.5", "@angular/cdk": "^20.0.3", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/material": "^20.0.3", "@angular/material-moment-adapter": "^20.0.5", "@angular/platform-browser": "^20.0.0", "@angular/router": "^20.0.0", "@angular/service-worker": "^20.1.0", "@fortawesome/fontawesome-free": "^6.7.2", "bootstrap": "^5.3.7", "crypto-js": "^4.2.0", "moment": "^2.30.1", "ngx-cookie-service": "^20.0.1", "rxjs": "~7.8.0", "sweetalert2": "^11.22.2", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.0.3", "@angular/cli": "^20.0.3", "@angular/compiler-cli": "^20.0.0", "@types/crypto-js": "^4.2.2", "@types/jasmine": "~5.1.0", "@types/moment": "^2.11.29", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}