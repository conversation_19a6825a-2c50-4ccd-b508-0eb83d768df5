.breadcrumb{
  margin-top: 20px;
}

.breadcrumb{
  margin-top: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-container {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.breadcrumb-item {
  font-weight: bold;
  color: #007bff;
  margin-right: 5px;
}

.required-info {
  margin-left: auto;
  font-size: 12px;
  color: #666;
}

.required-mark {
  color: #dc3545;
  font-weight: bold;
}

.separator {
  border: none;
  height: 1px;
  background-color: #e9ecef;
  margin: 20px 0;
}

.form-header {
  margin: 20px 0;
}

.form-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin: 40px 0;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: white;
}

.btn-primary:hover {
  background-color: var(----color-btn-primary-hover);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.disabled-form {
  pointer-events: none;
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .breadcrumb {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .required-info {
    margin-left: 0;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }
  
  .main-container {
    padding: 15px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}