.footer {
  background-color: var(--color-red-900);
  color: var(--color-text-inverse);
  padding: 3rem 1rem;
  font-size: 0.95rem;

  h5 {
    color: var(--color-red-100);
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .quick-links {
    li {
      margin-bottom: 0.5rem;

      a {
        color: var(--color-text-inverse);
        text-decoration: none;
        transition: color 0.3s;

        i {
          margin-right: 0.5rem;
          color: var(--color-red-500);
        }

        &:hover {
          color: var(--color-brand-light);

          i {
            color: var(--color-red-400);
          }
        }
      }
    }
  }

  .social {
    padding: 0;
    margin: 2rem 0;
    text-align: center;

    li {
      display: inline-block;
      margin: 0 0.5rem;

      a {
        color: var(--color-red-200);
        font-size: 1.25rem;
        transition: color 0.3s;

        &:hover {
          color: var(--color-red-100);
        }
      }
    }
  }

  a {
    color: var(--color-red-100);

    &:hover {
      text-decoration: underline;
      color: var(--color-brand-accent);
    }
  }

  .h6 {
    margin-top: 0.5rem;
    color: var(--color-red-300);
    font-size: 0.9rem;
  }

  p {
    margin: 0.5rem 0;
    color: var(--color-red-200);
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .footer-columns {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .footer-column {
      width: 100%;
      margin-bottom: 2rem;
    }
  }

  @media (max-width: 576px) {
    padding: 2rem 1rem;

    .text-sm-start {
      text-align: center !important;
    }

    .mb-4 {
      margin-bottom: 2rem !important;
    }

    h5 {
      font-size: 1.1rem;
    }

    .social {
      margin: 1.5rem 0;

      li {
        margin: 0 0.25rem;
      }
    }
  }
}
