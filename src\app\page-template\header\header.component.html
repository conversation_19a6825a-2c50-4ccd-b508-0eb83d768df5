<header class="app-header">
  <div class="container">
    <div class="header-wrapper">
      <!-- Left side - Sidebar toggle (only mobile) -->
      <button class="menu-toggle nav-toggle" (click)="toggleSidebar()" [class.active]="isSidebarOpen" aria-label="Toggle Sidebar">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </button>
      
      <!-- Center (mobile) / Left (desktop) - Brand logo -->
      <div class="brand">
        <a href="#" class="logo">
          <span class="logo-text">Swaagat</span>
        </a>
      </div>
      
      <!-- Right side - Nav menu toggle (only mobile) -->
      <button class="menu-toggle sidebar-toggle" (click)="toggleMenu()" [class.active]="isMenuOpen" aria-label="Toggle Navigation">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </button>
      
      <!-- Nav menu (center on desktop, slides from right on mobile) -->
      <nav class="nav-menu" [class.open]="isMenuOpen">
        <div class="nav-header" *ngIf="isMobile">
          <span>Navigation</span>
          <button class="close-nav" (click)="toggleMenu()">
            <span class="material-icons">close</span>
          </button>
        </div>
        <ul class="nav-list">
          <li class="nav-item"><a href="#" class="nav-link active">Home</a></li>
          <li class="nav-item"><a href="#" class="nav-link">About</a></li>
          <li class="nav-item"><a href="#" class="nav-link">Services</a></li>
          <li class="nav-item"><a href="#" class="nav-link">Projects</a></li>
          <li class="nav-item"><a href="#" class="nav-link">Contact</a></li>
        </ul>
        
        <!-- Mobile header actions inside nav menu -->
        <div class="mobile-header-actions" *ngIf="isMobile">
          <a href="#" class="action-button">Login</a>
        </div>
      </nav>

      <!-- Desktop header actions -->
      <div class="header-actions">
        <a href="#" class="action-button">Login</a>
      </div>
      
      <!-- Mobile overlay for nav menu -->
      <div class="mobile-nav-overlay" [class.active]="isMenuOpen" (click)="closeMenu()"></div>
      
      <!-- Mobile overlay for sidebar -->
      <div class="mobile-sidebar-overlay" [class.active]="isSidebarOpen" (click)="closeSidebar()"></div>
    </div>
  </div>
</header>